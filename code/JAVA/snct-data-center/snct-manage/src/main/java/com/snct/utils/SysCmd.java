package com.snct.utils;

import com.snct.serialport.SerialPortUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;


/**
 * 系统命令工具类
 * zfd
 * 2025/05/23
 **/
@Component
public class SysCmd {

    private static final Logger logger = LoggerFactory.getLogger(SerialPortUtil.class);

    /**
     * 重启系统
     */
    public void reboot() {
        try {
            logger.info("######执行reboot命令#####");
            // 执行命令
            Process process = Runtime.getRuntime().exec("reboot");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 执行命令
     */
    public void cmd(String cmd) {
        try {
            logger.info("执行cmd命令:{}", cmd);
            // 执行命令
            Process process = Runtime.getRuntime().exec(cmd);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
            // 等待命令执行完成
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 执行重启脚本命令（独立进程）
     *
     * @param cmd
     */
    public void executeRestartScript(String cmd) {
        try {

            String nohupCmd = "nohup " + cmd + " > /dev/null 2>&1 &";

            // 使用bash -c执行复合命令
            String[] bashCmd = {"/bin/bash", "-c", nohupCmd};

            // 启动进程但不等待完成
            Process process = Runtime.getRuntime().exec(bashCmd);

        } catch (Exception e) {
            logger.error("执行重启脚本时发生异常: {}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取系统当前时间
     *
     * @return 系统时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public String getCurrentSystemTime() {
        try {
            //logger.info("######获取系统时间#####");

            // 使用bash -c执行命令，确保命令正确解析
            String[] cmd = {"/bin/bash", "-c", "date '+%Y-%m-%d %H:%M:%S'"};
            Process process = Runtime.getRuntime().exec(cmd);

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"));
            String result = reader.readLine();

            int exitCode = process.waitFor();

            if (exitCode != 0 || result == null || result.trim().isEmpty()) {
                logger.error("获取系统时间失败");
                return null;
            }

            logger.info("系统时间: {}", result.trim());
            return result.trim();

        } catch (Exception e) {
            logger.error("error获取系统时间失败");
            //e.printStackTrace();
            return null;
        }
    }

    /**
     * 设置系统时间
     *
     * @param dateTime 时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 是否设置成功
     */
    public boolean setSystemTime(String dateTime) {
        try {
            logger.info("######设置系统时间: {}", dateTime + "#####");

            // 使用bash -c执行命令，确保命令正确解析
            String[] cmd = {"/bin/bash", "-c", "date -s '" + dateTime + "'"};
            Process process = Runtime.getRuntime().exec(cmd);

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                logger.debug(line);
            }

            int exitCode = process.waitFor();
            logger.info("设置系统时间命令执行结果: {}", exitCode);

            return exitCode == 0;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
