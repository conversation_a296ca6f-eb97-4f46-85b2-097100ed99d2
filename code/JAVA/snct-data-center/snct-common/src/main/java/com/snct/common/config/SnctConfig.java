package com.snct.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "snct")
public class SnctConfig
{
    /** 项目名称 */
    private String name;

    /** 版本 */
    private static String version;

    /** 版权年份 */
    private String copyrightYear;

    /** 上传路径 */
    private static String profileForLinux;
    /** 上传路径 */
    private static String profileForWindos;

    /** 获取地址开关 */
    private static boolean addressEnabled;

    /** 验证码类型 */
    private static String captchaType;

    /** 系统部署路径 */
    private static String devUrl;

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public static String getVersion()
    {
        return version;
    }

    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getCopyrightYear()
    {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear)
    {
        this.copyrightYear = copyrightYear;
    }

    public static String getProfile()
    {
        String osName = System.getProperty("os.name").toLowerCase();
        if(osName.indexOf("win") >= 0){
            return profileForWindos;
        }else{
            return profileForLinux;
        }
    }

    public static String getProfileForWindos()
    {
        return profileForWindos;
    }
    public static String getProfileForLinux()
    {
        return profileForLinux;
    }

    public void setProfileForWindos(String profileForWindos)
    {
        SnctConfig.profileForWindos = profileForWindos;
    }
    public void setProfileForLinux(String profileForLinux)
    {
        SnctConfig.profileForLinux = profileForLinux;
    }

    public static boolean isAddressEnabled()
    {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled)
    {
        SnctConfig.addressEnabled = addressEnabled;
    }

    public static String getCaptchaType() {
        return captchaType;
    }

    public void setCaptchaType(String captchaType) {
        SnctConfig.captchaType = captchaType;
    }

    /**
     * 获取导入上传路径
     */
    public static String getImportPath()
    {
        return getProfile() + "/import";
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath()
    {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath()
    {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath()
    {
        return getProfile() + "/upload";
    }

    public static String getDevUrl()
    {
        return devUrl;
    }

    public void setDevUrl(String devUrl)
    {
        SnctConfig.devUrl = devUrl;
    }

}
