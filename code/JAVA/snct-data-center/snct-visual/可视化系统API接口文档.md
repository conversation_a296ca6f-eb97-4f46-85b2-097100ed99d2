# 可视化系统API接口文档

## 目录
1. [登录接口文档](#1-登录接口文档)
2. [验证码接口文档](#2-验证码接口文档)
3. [WebSocket连接文档](#3-websocket连接文档)

---

## 1. 登录接口文档

### 1.1 用户登录接口

#### 接口信息
- **接口路径**: `/login`
- **请求方法**: `POST`
- **接口描述**: 用户登录验证，支持两种模式（管理端和可视化端）

#### 管理端登录 (snct-manage)

**请求参数**:
```json
{
  "username": "string",    // 用户名
  "password": "string",    // 密码
  "code": "string",        // 验证码
  "uuid": "string"         // 验证码唯一标识
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9..."
}
```

#### 可视化端登录 (snct-visual)

**请求参数**:
```
username: string    // 用户名
password: string    // 密码
type: integer      // 验证码类型 (1=需要验证码, 其他=不需要)
code: string       // 验证码 (type=1时必填)
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9...",
  "type": "U01"     // 用户权限类型: U00=无权限, U01=最大权限, U02=三方企业
}
```

### 1.2 获取用户信息接口

#### 接口信息
- **接口路径**: `/getInfo`
- **请求方法**: `GET`
- **接口描述**: 获取当前登录用户的详细信息
- **认证要求**: 需要Bearer Token

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "user": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "",
    "deptId": 100,
    "status": "0"
  },
  "roles": ["admin"],
  "permissions": ["*:*:*"]
}
```

### 1.3 获取路由信息接口

#### 接口信息
- **接口路径**: `/getRouters`
- **请求方法**: `GET`
- **接口描述**: 获取用户可访问的菜单路由信息
- **认证要求**: 需要Bearer Token

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "name": "System",
      "path": "/system",
      "component": "Layout",
      "meta": {
        "title": "系统管理",
        "icon": "system"
      },
      "children": [...]
    }
  ]
}
```

### 1.4 个人信息接口 (仅可视化端)

#### 接口信息
- **接口路径**: `/profile`
- **请求方法**: `GET`
- **接口描述**: 获取当前用户的个人信息
- **认证要求**: 需要Bearer Token

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>"
  }
}
```

---

## 2. 验证码接口文档

### 2.1 生成验证码接口 (管理端)

#### 接口信息
- **接口路径**: `/captchaImage`
- **请求方法**: `GET`
- **接口描述**: 生成图形验证码
- **认证要求**: 无需认证

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "captchaEnabled": true,
  "uuid": "a1b2c3d4e5f6",
  "img": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**字段说明**:
- `captchaEnabled`: 是否启用验证码
- `uuid`: 验证码唯一标识，登录时需要传递
- `img`: Base64编码的验证码图片

### 2.2 获取滑动验证码接口 (可视化端)

#### 接口信息
- **接口路径**: `/system/captcha/get`
- **请求方法**: `POST`
- **接口描述**: 获取滑动验证码
- **认证要求**: 无需认证

**请求参数**:
```json
{
  "captchaType": "blockPuzzle"
}
```

**响应格式**:
```json
{
  "repCode": "0000",
  "repMsg": "success",
  "repData": {
    "originalImageBase64": "data:image/png;base64,...",
    "jigsawImageBase64": "data:image/png;base64,...",
    "token": "verification_token",
    "secretKey": "secret_key"
  }
}
```

### 2.3 校验滑动验证码接口 (可视化端)

#### 接口信息
- **接口路径**: `/system/captcha/check`
- **请求方法**: `POST`
- **接口描述**: 校验滑动验证码
- **认证要求**: 无需认证

**请求参数**:
```json
{
  "captchaType": "blockPuzzle",
  "token": "verification_token",
  "pointJson": "{\"x\":100,\"y\":50}"
}
```

**响应格式**:
```json
{
  "repCode": "0000",
  "repMsg": "success",
  "repData": {
    "captchaVerification": "verification_code"
  }
}
```

### 2.4 测试接口 (管理端)

#### 接口信息
- **接口路径**: `/open`
- **请求方法**: `POST`
- **接口描述**: 测试接口，用于调试
- **认证要求**: 无需认证

**请求参数**:
```json
{
  "data": "任意JSON数据"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

## 3. WebSocket连接文档

### 3.1 WebSocket连接端点

#### 连接信息
- **连接地址**: `ws://host:port/websocket/{token}`
- **协议**: WebSocket
- **认证方式**: URL路径参数传递token

#### 连接参数
- `token`: 用户登录后获得的JWT令牌

### 3.2 连接建立流程

1. **客户端发起连接**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080/websocket/' + token);
   ```

2. **服务端验证token**
   - 验证token有效性
   - 获取用户信息和权限
   - 建立会话映射

3. **连接成功响应**
   ```json
   {
     "token": "eyJhbGciOiJIUzUxMiJ9...",
     "time": "1640995200",
     "type": "U01"
   }
   ```

4. **连接失败响应**
   ```json
   {
     "code": "0009",
     "token": "invalid_token",
     "time": "1640995200"
   }
   ```

### 3.3 消息通信协议

#### 请求数据包格式
```
type66#{deptId}#{sn}#{codes}
```

**参数说明**:
- `type66`: 固定标识，表示请求数据包
- `deptId`: 企业ID
- `sn`: 船只编号 (首页使用0)
- `codes`: 数据包代码，多个用逗号分隔

#### 数据包代码说明

**企业级数据 (0A系列)**:
- `0A01`: 入驻企业列表
- `0A02`: 入驻企业/船只数量汇总
- `0A03`: 天气信息/预警信息
- `0A04`: 地图数据-企业名称与坐标列表

**船只数据 (0B系列)**:
- `0B01`: 船基本信息
- `0B02`: 姿态信息
- `0B03`: 气象信息
- `0B04`: 天气信息/预警信息
- `0B05`: 企业总接入船只数量
- `0B06`: 地图数据-船只名称与坐标列表

**视频监控 (0C系列)**:
- `0C01`: 视频监控实时播放地址列表
- `0C02`: 视频监控历史记录列表

**轨迹数据 (0D系列)**:
- `0D01`: 船基本信息
- `0D02`: 历史轨迹节点信息

**设备数据 (0E系列)**:
- `0E01`: PDU信息
- `0E02`: 卫星猫信息
- `0E03`: 功放信息

#### 请求示例
```
type66#101#SN001#0B01,0B02,0B03
```

#### 响应数据格式
```json
{
  "data": {
    // 具体数据内容
  },
  "code": "0B01",
  "deptId": "101",
  "sn": "SN001"
}
```

### 3.4 权限控制

#### 用户权限类型
- `U01`: 最大权限账号 (deptId ≤ 100)
- `U02`: 三方企业账号 (deptId > 100)

#### 权限限制
- `U02`类型用户不能请求`0A`系列数据包
- 违反权限规则将返回错误码`0010`并关闭连接

### 3.5 连接管理

#### 自动清理机制
- 连接建立后60秒内未发送有效消息将被自动关闭
- 发送数据异常的连接将被自动清理
- 所有连接断开后，数据推送线程将停止

#### 数据推送频率
- 每5秒推送一次数据
- 支持多个数据包代码同时请求
- 实时推送最新数据

### 3.6 错误处理

#### 错误码说明
- `0009`: Token验证失败
- `0010`: 权限不足，请求了无权访问的数据包

#### 异常处理
- 连接异常时自动重连
- 数据格式错误时记录日志
- 无效消息直接忽略

---

## 4. 通用响应格式

### 4.1 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 4.2 错误响应
```json
{
  "code": 500,
  "msg": "系统异常",
  "data": null
}
```

### 4.3 常见状态码
- `200`: 操作成功
- `401`: 未授权
- `403`: 权限不足
- `500`: 系统异常

---

## 5. 安全说明

### 5.1 认证机制
- 使用JWT Token进行身份认证
- Token包含用户信息和权限
- Token有过期时间限制

### 5.2 权限控制
- 基于角色的权限控制
- 接口级权限验证
- 数据级权限隔离

### 5.3 安全建议
- 使用HTTPS协议传输
- 定期更新Token
- 监控异常访问行为
