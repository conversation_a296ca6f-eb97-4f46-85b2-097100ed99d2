# 可视化系统API接口文档

## 目录
1. [登录接口文档](#1-登录接口文档)
2. [验证码接口文档](#2-验证码接口文档)
3. [WebSocket连接文档](#3-websocket连接文档)

---

## 1. 登录接口文档

### 1.1 用户登录接口

#### 接口信息
- **接口路径**: `/login`
- **请求方法**: `POST`
- **接口描述**: 用户登录验证，支持两种模式（管理端和可视化端）

#### 管理端登录 (snct-manage)

**请求参数**:
```json
{
  "username": "string",    // 用户名
  "password": "string",    // 密码
  "code": "string",        // 验证码
  "uuid": "string"         // 验证码唯一标识
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9..."
}
```

#### 可视化端登录 (snct-visual)

**请求参数**:
```
username: string    // 用户名
password: string    // 密码
type: integer      // 验证码类型 (1=需要验证码, 其他=不需要)
code: string       // 验证码 (type=1时必填)
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzUxMiJ9...",
  "type": "U01"     // 用户权限类型: U00=无权限, U01=最大权限, U02=三方企业
}
```

### 1.2 获取用户信息接口

#### 接口信息
- **接口路径**: `/getInfo`
- **请求方法**: `GET`
- **接口描述**: 获取当前登录用户的详细信息
- **认证要求**: 需要Bearer Token

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "user": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "",
    "deptId": 100,
    "status": "0"
  },
  "roles": ["admin"],
  "permissions": ["*:*:*"]
}
```

### 1.3 获取路由信息接口

#### 接口信息
- **接口路径**: `/getRouters`
- **请求方法**: `GET`
- **接口描述**: 获取用户可访问的菜单路由信息
- **认证要求**: 需要Bearer Token

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "name": "System",
      "path": "/system",
      "component": "Layout",
      "meta": {
        "title": "系统管理",
        "icon": "system"
      },
      "children": [...]
    }
  ]
}
```

### 1.4 个人信息接口 (仅可视化端)

#### 接口信息
- **接口路径**: `/profile`
- **请求方法**: `GET`
- **接口描述**: 获取当前用户的个人信息
- **认证要求**: 需要Bearer Token

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>"
  }
}
```

---

## 2. 验证码接口文档

### 2.1 生成验证码接口 (管理端)

#### 接口信息
- **接口路径**: `/captchaImage`
- **请求方法**: `GET`
- **接口描述**: 生成图形验证码
- **认证要求**: 无需认证

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "captchaEnabled": true,
  "uuid": "a1b2c3d4e5f6",
  "img": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**字段说明**:
- `captchaEnabled`: 是否启用验证码
- `uuid`: 验证码唯一标识，登录时需要传递
- `img`: Base64编码的验证码图片

### 2.2 获取滑动验证码接口 (可视化端)

#### 接口信息
- **接口路径**: `/system/captcha/get`
- **请求方法**: `POST`
- **接口描述**: 获取滑动验证码
- **认证要求**: 无需认证

**请求参数**:
```json
{
  "captchaType": "blockPuzzle"
}
```

**响应格式**:
```json
{
  "repCode": "0000",
  "repMsg": "success",
  "repData": {
    "originalImageBase64": "data:image/png;base64,...",
    "jigsawImageBase64": "data:image/png;base64,...",
    "token": "verification_token",
    "secretKey": "secret_key"
  }
}
```

### 2.3 校验滑动验证码接口 (可视化端)

#### 接口信息
- **接口路径**: `/system/captcha/check`
- **请求方法**: `POST`
- **接口描述**: 校验滑动验证码
- **认证要求**: 无需认证

**请求参数**:
```json
{
  "captchaType": "blockPuzzle",
  "token": "verification_token",
  "pointJson": "{\"x\":100,\"y\":50}"
}
```

**响应格式**:
```json
{
  "repCode": "0000",
  "repMsg": "success",
  "repData": {
    "captchaVerification": "verification_code"
  }
}
```

### 2.4 测试接口 (管理端)

#### 接口信息
- **接口路径**: `/open`
- **请求方法**: `POST`
- **接口描述**: 测试接口，用于调试
- **认证要求**: 无需认证

**请求参数**:
```json
{
  "data": "任意JSON数据"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

## 3. WebSocket连接文档

### 3.1 WebSocket连接端点

#### 连接信息
- **连接地址**: `ws://host:port/websocket/{token}`
- **协议**: WebSocket
- **认证方式**: URL路径参数传递token

#### 连接参数
- `token`: 用户登录后获得的JWT令牌

### 3.2 连接建立流程

1. **客户端发起连接**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080/websocket/' + token);
   ```

2. **服务端验证token**
   - 验证token有效性
   - 获取用户信息和权限
   - 建立会话映射

3. **连接成功响应**
   ```json
   {
     "token": "eyJhbGciOiJIUzUxMiJ9...",
     "time": "1640995200",
     "type": "U01"
   }
   ```

4. **连接失败响应**
   ```json
   {
     "code": "0009",
     "token": "invalid_token",
     "time": "1640995200"
   }
   ```

### 3.3 消息通信协议

#### 请求数据包格式
```
type66#{deptId}#{sn}#{codes}
```

**参数说明**:
- `type66`: 固定标识，表示请求数据包
- `deptId`: 企业ID
- `sn`: 船只编号 (首页使用0)
- `codes`: 数据包代码，多个用逗号分隔

#### 数据包代码说明

**企业级数据 (0A系列)**:
- `0A01`: 入驻企业列表
- `0A02`: 入驻企业/船只数量汇总
- `0A03`: 天气信息/预警信息
- `0A04`: 地图数据-企业名称与坐标列表

**船只数据 (0B系列)**:
- `0B01`: 船基本信息
- `0B02`: 姿态信息
- `0B03`: 气象信息
- `0B04`: 天气信息/预警信息
- `0B05`: 企业总接入船只数量
- `0B06`: 地图数据-船只名称与坐标列表

**视频监控 (0C系列)**:
- `0C01`: 视频监控实时播放地址列表
- `0C02`: 视频监控历史记录列表

**轨迹数据 (0D系列)**:
- `0D01`: 船基本信息
- `0D02`: 历史轨迹节点信息

**设备数据 (0E系列)**:
- `0E01`: PDU信息
- `0E02`: 卫星猫信息
- `0E03`: 功放信息

#### 请求示例
```
type66#101#SN001#0B01,0B02,0B03
```

#### 响应数据格式
```json
{
  "data": {
    // 具体数据内容，根据不同的code返回不同结构
  },
  "code": "0B01",
  "deptId": "101",
  "sn": "SN001"
}
```

### 3.7 数据包代码对照表

| 代码 | 名称 | 描述 | 权限要求 | 实现状态 |
|------|------|------|----------|----------|
| 0A01 | 入驻企业列表 | 获取所有入驻企业的基本信息 | U01 | ✅ 已实现 |
| 0A02 | 企业/船只数量汇总 | 统计企业和船只的总数量 | U01 | ✅ 已实现 |
| 0A03 | 天气信息/预警信息 | 天气预报和预警信息 | U01 | ❌ 暂未实现 |
| 0A04 | 地图数据-企业坐标 | 企业名称与坐标列表 | U01 | ✅ 已实现 |
| 0B01 | 船基本信息 | 船舶的基础信息和GPS位置 | U01/U02 | ✅ 已实现 |
| 0B02 | 姿态信息 | 船舶姿态仪数据 | U01/U02 | ✅ 已实现 |
| 0B03 | 气象信息 | 船舶气象仪数据 | U01/U02 | ✅ 已实现 |
| 0B04 | 天气信息/预警信息 | 船舶相关天气预警 | U01/U02 | ❌ 暂未实现 |
| 0B05 | 企业船只数量 | 指定企业的船只总数 | U01/U02 | ✅ 已实现 |
| 0B06 | 地图数据-船只坐标 | 船只名称与坐标列表 | U01/U02 | ✅ 已实现 |
| 0C01 | 视频监控实时地址 | 实时视频播放地址列表 | U01/U02 | ❌ 暂未实现 |
| 0C02 | 视频监控历史记录 | 历史视频记录列表 | U01/U02 | ❌ 暂未实现 |
| 0D01 | 船基本信息 | 与0B01相同的船舶信息 | U01/U02 | ✅ 已实现 |
| 0D02 | 历史轨迹节点 | 船舶历史轨迹数据 | U01/U02 | ❌ 暂未实现 |
| 0E01 | PDU信息 | 电源分配单元数据 | U01/U02 | ❌ 暂未实现 |
| 0E02 | 卫星猫信息 | 卫星调制解调器数据 | U01/U02 | ❌ 暂未实现 |
| 0E03 | 功放信息 | 功率放大器设备数据 | U01/U02 | ❌ 暂未实现 |

### 3.8 详细数据格式说明

#### 企业级数据 (0A系列)

**0A01 - 入驻企业列表**
```json
{
  "data": [
    {
      "deptId": 101,
      "deptName": "企业名称",
      "status": "0"
    }
  ],
  "code": "0A01",
  "deptId": "0",
  "sn": "0"
}
```

**0A02 - 入驻企业/船只数量汇总**
```json
{
  "data": {
    "enterprise_num": 5,
    "ship_num": 15
  },
  "code": "0A02",
  "deptId": "0",
  "sn": "0"
}
```

**0A03 - 天气信息/预警信息**
```json
{
  "data": {},
  "code": "0A03",
  "deptId": "101",
  "sn": "0"
}
```
*注：当前版本暂无天气和预警信息*

**0A04 - 地图数据-企业名称与坐标列表**
```json
{
  "data": [
    {
      "name": "船舶名称",
      "sn": "SN001",
      "shipId": "1",
      "longitude": "120.123456",
      "latitude": "30.123456",
      "latitudeHemisphere": "N",
      "longitudeHemisphere": "E",
      "utc": "123456.00"
    }
  ],
  "code": "0A04",
  "deptId": "101",
  "sn": "0"
}
```

#### 船只数据 (0B系列)

**0B01 - 船基本信息**
```json
{
  "data": {
    "name": "船舶名称",
    "mmsi": "123456789",
    "callSign": "CALL001",
    "sn": "SN001",
    "longitude": "120.123456",
    "latitude": "30.123456",
    "latitudeHemisphere": "N",
    "longitudeHemisphere": "E",
    "utc": "123456.00"
  },
  "code": "0B01",
  "deptId": "101",
  "sn": "SN001"
}
```

**0B02 - 姿态信息**
```json
{
  "data": {
    "attitudeHeading": "123.45",      // 船首向
    "attitudeRolling": "1.23",        // 横摇
    "attitudePitch": "2.34",          // 纵摇
    "attitudeLongitude": "120.123456", // 经度
    "attitudeLatitude": "30.123456",   // 纬度
    "attitudeHeight": "10.5",         // 高度
    "attitudeSpeed": "15.2",          // 速度
    "attitudeDistance": "1250.5",     // 距离
    "attitudeUptime": "2024-01-01 12:00:00", // 数据更新时间
    "attitudeSn": "SN001"
  },
  "code": "0B02",
  "deptId": "101",
  "sn": "SN001"
}
```

**0B03 - 气象信息**
```json
{
  "data": {
    "awsSpeed": "12.5",               // 相对风速
    "awsDirection": "180.5",          // 相对风向
    "awsUptime": "2024-01-01 12:00:00", // 数据更新时间
    "awsSn": "SN001"
  },
  "code": "0B03",
  "deptId": "101",
  "sn": "SN001"
}
```

**0B04 - 天气信息/预警信息**
```json
{
  "data": {},
  "code": "0B04",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂无天气和预警信息*

**0B05 - 企业总接入船只数量**
```json
{
  "data": {
    "shipNum": 8
  },
  "code": "0B05",
  "deptId": "101",
  "sn": "0"
}
```

**0B06 - 地图数据-船只名称与坐标列表**
```json
{
  "data": [
    {
      "name": "船舶名称",
      "sn": "SN001",
      "shipId": "1",
      "longitude": "120.123456",
      "latitude": "30.123456",
      "latitudeHemisphere": "N",
      "longitudeHemisphere": "E",
      "utc": "123456.00"
    }
  ],
  "code": "0B06",
  "deptId": "101",
  "sn": "0"
}
```

#### 视频监控 (0C系列)

**0C01 - 视频监控实时播放地址列表**
```json
{
  "data": {},
  "code": "0C01",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂未实现*

**0C02 - 视频监控历史记录列表**
```json
{
  "data": {},
  "code": "0C02",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂未实现*

#### 轨迹数据 (0D系列)

**0D01 - 船基本信息**
```json
{
  "data": {
    "name": "船舶名称",
    "mmsi": "123456789",
    "callSign": "CALL001",
    "sn": "SN001",
    "longitude": "120.123456",
    "latitude": "30.123456",
    "latitudeHemisphere": "N",
    "longitudeHemisphere": "E",
    "utc": "123456.00"
  },
  "code": "0D01",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：与0B01数据结构相同*

**0D02 - 历史轨迹节点信息**
```json
{
  "data": {},
  "code": "0D02",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂未实现*

#### 设备数据 (0E系列)

**0E01 - PDU信息**
```json
{
  "data": {},
  "code": "0E01",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂未实现，预期数据结构包含：*
- `manage`: 总电能
- `electric`: 电流
- `voltage`: 电压
- `yesPwoer`: 有功功率
- `noPwoer`: 无功功率
- `seePwoer`: 视在功率
- `powerParam`: 功率因数

**0E02 - 卫星猫信息**
```json
{
  "data": {},
  "code": "0E02",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂未实现，预期数据结构包含：*
- `signal`: 信号强度
- `speed`: 速率
- `sendPower`: 发送功率
- `isFlag`: 状态标志

**0E03 - 功放信息**
```json
{
  "data": {},
  "code": "0E03",
  "deptId": "101",
  "sn": "SN001"
}
```
*注：当前版本暂未实现，预期数据结构包含：*
- `decay`: 衰减值
- `temp`: 温度
- `outPower`: 输出功率
- `bucStatus`: 设备状态

### 3.9 GPS数据详细字段说明

GPS数据(0B01、0D01中包含)的完整字段结构：
```json
{
  "utcTime": "123456.00",           // UTC时间
  "latitude": "30.123456",          // 纬度
  "longitude": "120.123456",        // 经度
  "latitudeHemisphere": "N",        // 纬度半球(N/S)
  "longitudeHemisphere": "E",       // 经度半球(E/W)
  "position": "1",                  // 定位质量指示(0=无效)
  "satellites": "08",               // 使用卫星数量
  "definition": "1.2",              // 水平精确度
  "antennaHeight": "10.5",          // 天线离海平面高度
  "geoidHeight": "25.3",            // 大地水准面高度
  "speed": "15.2",                  // 对地速度(节)
  "course": "123.4",                // 对地航向(度)
  "day": "01",                      // 日
  "month": "01",                    // 月
  "year": "2024"                    // 年
}
```

### 3.10 姿态仪数据详细字段说明

姿态仪数据(0B02)的完整字段结构：
```json
{
  "heading": "123.45",              // 船首向/航向(度)
  "rolling": "1.23",                // 横摇角(度)
  "pitch": "2.34",                  // 纵摇角(度)
  "lon": "120.123456",              // 经度
  "lat": "30.123456",               // 纬度
  "elpHeight": "10.5",              // 椭球高度(米)
  "velG": "15.2",                   // 地速(m/s)
  "initialBjTime": "2024-01-01 12:00:00", // 数据时间戳
  "headingIndicator": "A",          // 定向指示状态
  "svn": "12",                      // 主导天线收星数
  "diffAge": "1.5",                 // 差分延迟
  "stationId": "1001",              // 基准站ID
  "baselineLength": "2.5",          // 基线长度(米)
  "solutionSv": "10"                // 从站参与解算的卫星数
}
```

### 3.11 气象数据详细字段说明

气象数据(0B03)的完整字段结构：
```json
{
  "relativeWind": "180.5",          // 相对风向(度)
  "relativeWindSpeed": "12.5",      // 相对风速(m/s或节)
  "windLogoR": "R",                 // 相对风向标识
  "windSpeedUnit": "N",             // 风速单位(N=节,M=米/秒)
  "windStatus": "A",                // 风向风速状态(A=有效,V=无效)
  "trueWind": "185.2",              // 真风向(度)
  "trueWindSpeed": "10.8",          // 真风速
  "windLogoT": "T",                 // 真风向标识
  "initialBjTime": "2024-01-01 12:00:00", // 数据时间戳
  "latitude": "30.123456",          // 纬度
  "longitude": "120.123456"         // 经度
}
```

### 3.4 权限控制

#### 用户权限类型
- `U01`: 最大权限账号 (deptId ≤ 100)
- `U02`: 三方企业账号 (deptId > 100)

#### 权限限制
- `U02`类型用户不能请求`0A`系列数据包
- 违反权限规则将返回错误码`0010`并关闭连接

### 3.5 连接管理

#### 自动清理机制
- 连接建立后60秒内未发送有效消息将被自动关闭
- 发送数据异常的连接将被自动清理
- 所有连接断开后，数据推送线程将停止

#### 数据推送频率
- 每5秒推送一次数据
- 支持多个数据包代码同时请求
- 实时推送最新数据

### 3.6 使用示例

#### JavaScript客户端示例
```javascript
// 建立WebSocket连接
const token = 'your_jwt_token_here';
const ws = new WebSocket(`ws://localhost:8080/websocket/${token}`);

// 连接建立
ws.onopen = function(event) {
    console.log('WebSocket连接已建立');

    // 请求企业列表和船只基本信息
    ws.send('type66#101#SN001#0A01,0B01,0B02');
};

// 接收消息
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);

    switch(data.code) {
        case '0A01':
            console.log('企业列表:', data.data);
            break;
        case '0B01':
            console.log('船只基本信息:', data.data);
            updateShipInfo(data.data);
            break;
        case '0B02':
            console.log('姿态信息:', data.data);
            updateAttitudeDisplay(data.data);
            break;
        case '0009':
            console.error('Token验证失败');
            ws.close();
            break;
        case '0010':
            console.error('权限不足');
            ws.close();
            break;
    }
};

// 连接关闭
ws.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};

// 错误处理
ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

// 更新船只信息显示
function updateShipInfo(shipData) {
    document.getElementById('ship-name').textContent = shipData.name;
    document.getElementById('ship-position').textContent =
        `${shipData.latitude}°${shipData.latitudeHemisphere}, ${shipData.longitude}°${shipData.longitudeHemisphere}`;
}

// 更新姿态信息显示
function updateAttitudeDisplay(attitudeData) {
    document.getElementById('heading').textContent = attitudeData.attitudeHeading + '°';
    document.getElementById('roll').textContent = attitudeData.attitudeRolling + '°';
    document.getElementById('pitch').textContent = attitudeData.attitudePitch + '°';
}
```

#### 数据请求最佳实践

1. **合理组合数据包**：一次请求中可以组合多个相关的数据包
   ```
   // 船只综合信息
   type66#101#SN001#0B01,0B02,0B03

   // 企业概览信息
   type66#0#0#0A01,0A02,0A04
   ```

2. **权限检查**：根据用户类型请求相应的数据包
   ```javascript
   // U01用户可以请求所有数据
   if (userType === 'U01') {
       ws.send('type66#0#0#0A01,0A02');
   }
   // U02用户只能请求船只相关数据
   else if (userType === 'U02') {
       ws.send('type66#101#SN001#0B01,0B02,0B03');
   }
   ```

3. **错误重连机制**
   ```javascript
   let reconnectAttempts = 0;
   const maxReconnectAttempts = 5;

   function reconnect() {
       if (reconnectAttempts < maxReconnectAttempts) {
           reconnectAttempts++;
           setTimeout(() => {
               console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
               connectWebSocket();
           }, 1000 * reconnectAttempts);
       }
   }
   ```

### 3.7 注意事项

#### 数据更新频率
- 系统每5秒推送一次数据更新
- 客户端应该准备处理高频数据更新
- 建议使用防抖或节流机制避免UI频繁刷新

#### 连接管理
- 连接建立后60秒内必须发送有效的数据请求
- 长时间无活动的连接会被自动清理
- 建议实现心跳机制保持连接活跃

#### 数据处理
- 所有数值类型的数据都以字符串形式传输
- 客户端需要根据业务需求进行类型转换
- 空数据或未实现的功能返回空对象 `{}`

#### 性能优化
- 避免同时请求过多数据包
- 根据页面需求按需请求数据
- 合理使用缓存减少重复请求

### 3.8 错误处理

#### 错误码说明
- `0009`: Token验证失败
- `0010`: 权限不足，请求了无权访问的数据包

#### 异常处理
- 连接异常时自动重连
- 数据格式错误时记录日志
- 无效消息直接忽略

---

## 4. 通用响应格式

### 4.1 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 4.2 错误响应
```json
{
  "code": 500,
  "msg": "系统异常",
  "data": null
}
```

### 4.3 常见状态码
- `200`: 操作成功
- `401`: 未授权
- `403`: 权限不足
- `500`: 系统异常

---

## 5. 安全说明

### 5.1 认证机制
- 使用JWT Token进行身份认证
- Token包含用户信息和权限
- Token有过期时间限制

### 5.2 权限控制
- 基于角色的权限控制
- 接口级权限验证
- 数据级权限隔离

### 5.3 安全建议
- 使用HTTPS协议传输
- 定期更新Token
- 监控异常访问行为
